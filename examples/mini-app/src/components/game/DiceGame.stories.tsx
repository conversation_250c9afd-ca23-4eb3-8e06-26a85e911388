import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react"
import gameBg1 from "../../assets/game/game-background-9.png"
import gameBg4 from "../../assets/game/game-background-10.png"
import gameBg2 from "../../assets/game/game-background-11.webp"
import gameBg3 from "../../assets/game/game-background-12.png"
import gameBg5 from "../../assets/game/game-background-13.png"
import gameBg6 from "../../assets/game/game-background-14.png"
import { AppProviders } from "../../providers"
import { DiceGame } from "./DiceGame"

const meta = {
  title: "Game/DiceGame",
  component: DiceGame,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "light",
      values: [
        { name: "light", value: "#FFFFFF" },
        { name: "dark", value: "oklch(0.15 0 0)" },
      ],
    },
    loki: {
      skip: true,
    },
  },
  decorators: [
    (Story) => (
      <AppProviders>
        <Story />
      </AppProviders>
    ),
  ],
  tags: ["autodocs"],
  argTypes: {
    theme: {
      control: "radio",
      options: ["light", "dark", "system"],
    },
    customTheme: {
      control: "object",
      description: "Custom theme",
      table: {
        type: {
          summary: "object",
          detail: `{
            "--primary": string,
            "--play-btn-font": string,
            "--game-window-overlay": string,
          }`,
        },
      },
    },
    backgroundImage: {
      control: "file",
      description: "Background image",
      accept: "image/*",
    },
  },
} satisfies Meta<typeof DiceGame>

export default meta
type Story = StoryObj<typeof meta>

const Template: Story = {
  render: (args) => <DiceGame {...args} />,
}

export const LightTheme: Story = {
  ...Template,
  args: {
    theme: "light",
  },
}

export const DarkTheme: Story = {
  ...Template,
  args: {
    theme: "dark",
    backgroundImage: gameBg2,
  },
  parameters: {
    backgrounds: { default: "light" },
  },
}

export const SystemTheme: Story = {
  ...Template,
  args: {
    theme: "system",
    backgroundImage: gameBg4,
  },
  parameters: {
    chromatic: { disable: true },
  },
}

export const DonutRollLightTheme: Story = {
  ...Template,
  args: {
    theme: "light",
    customTheme: {
      "--primary": "#7f5058",
      "--play-btn-font": "rgb(238 231 235)",
    } as React.CSSProperties,
    backgroundImage: gameBg5,
  },
  parameters: {
    chromatic: { disable: true },
  },
}

export const SatelliteDarkTheme: Story = {
  ...Template,
  args: {
    theme: "dark",
    customTheme: {
      "--primary": "#d7caab",
      "--play-btn-font": "#254450",
    } as React.CSSProperties,
    backgroundImage: gameBg1,
  },
  parameters: {
    chromatic: { disable: true },
  },
}

export const SpaceshipDarkTheme: Story = {
  ...Template,
  args: {
    theme: "dark",
    customTheme: {
      "--primary": "#595b5c",
      "--play-btn-font": "#c5c2ab",
    } as React.CSSProperties,
    backgroundImage: gameBg3,
  },
  parameters: {
    chromatic: { disable: true },
  },
}

export const MysticForestDarkTheme: Story = {
  ...Template,
  args: {
    theme: "dark",
    customTheme: {
      "--primary": "rgb(74 41 24)",
      "--play-btn-font": "rgb(225 159 31)",
    } as React.CSSProperties,
    backgroundImage: gameBg6,
  },
  parameters: {
    chromatic: { disable: true },
  },
}
