import { ROULETTE_INPUT_BUNDLE, Roulette<PERSON>umber } from "@betswirl/sdk-core"
import chipDisabledSvg from "../../assets/game/roulette-chip-disabled.svg"
import chipSvg from "../../assets/game/roulette-chip.svg"
import { Button } from "../ui/button"
import { GameMultiplierDisplay } from "./shared/GameMultiplierDisplay"
import { GameControlsProps } from "./shared/types"

interface RouletteGameControlsProps extends GameControlsProps {
  selectedNumbers: RouletteNumber[]
  onNumbersChange: (numbers: RouletteNumber[]) => void
}

const RED_NUMBERS: RouletteNumber[] = [
  1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36,
]
const BLACK_NUMBERS: RouletteNumber[] = [
  2, 4, 6, 8, 10, 11, 13, 15, 17, 20, 22, 24, 26, 28, 29, 31, 33, 35,
]

type RouletteColor = "red" | "black" | "green"

interface ButtonColorConfig {
  background: string
  hover: string
}

const ROULETTE_COLORS: Record<RouletteColor, ButtonColorConfig> = {
  red: {
    background: "bg-roulette-red",
    hover: "hover:bg-roulette-red-hover",
  },
  black: {
    background: "bg-roulette-black",
    hover: "hover:bg-roulette-black-hover",
  },
  green: {
    background: "bg-roulette-green",
    hover: "hover:bg-roulette-green-hover",
  },
}

const BUNDLE_COLORS: ButtonColorConfig = {
  background: "bg-roulette-bundle",
  hover: "hover:bg-roulette-bundle-hover",
}

const getBundleStyles = (): string => {
  return `${BUNDLE_COLORS.background} ${BUNDLE_COLORS.hover}`
}

const DISABLED_STYLES =
  "disabled:bg-roulette-disabled disabled:text-roulette-disabled-text disabled:opacity-100"
const COMMON_BUTTON_STYLES = "text-white hover:text-white disabled:hover:bg-opacity-100"

const getNumberColor = (number: RouletteNumber): RouletteColor => {
  if (number === 0) return "green"
  return RED_NUMBERS.includes(number) ? "red" : "black"
}

const getColorStyles = (color: RouletteColor): string => {
  const colorConfig = ROULETTE_COLORS[color]
  return `${colorConfig.background} ${colorConfig.hover}`
}

export function RouletteGameControls({
  selectedNumbers,
  onNumbersChange,
  multiplier,
  isDisabled,
}: RouletteGameControlsProps) {
  const isNumberSelected = (number: RouletteNumber) => selectedNumbers.includes(number)

  const handleNumberClick = (number: RouletteNumber) => {
    if (isDisabled) return

    if (isNumberSelected(number)) {
      onNumbersChange(selectedNumbers.filter((n) => n !== number))
    } else {
      onNumbersChange([...selectedNumbers, number])
    }
  }

  const handleBundleClick = (bundle: ROULETTE_INPUT_BUNDLE) => {
    if (isDisabled) return

    let bundleNumbers: RouletteNumber[] = []

    switch (bundle) {
      case ROULETTE_INPUT_BUNDLE.RED:
        bundleNumbers = RED_NUMBERS
        break
      case ROULETTE_INPUT_BUNDLE.BLACK:
        bundleNumbers = BLACK_NUMBERS
        break
      case ROULETTE_INPUT_BUNDLE.ODD:
        bundleNumbers = Array.from({ length: 18 }, (_, i) => (i * 2 + 1) as RouletteNumber)
        break
      case ROULETTE_INPUT_BUNDLE.EVEN:
        bundleNumbers = Array.from({ length: 18 }, (_, i) => ((i + 1) * 2) as RouletteNumber)
        break
      case ROULETTE_INPUT_BUNDLE.ONE_TO_EIGHTEEN:
        bundleNumbers = Array.from({ length: 18 }, (_, i) => (i + 1) as RouletteNumber)
        break
      case ROULETTE_INPUT_BUNDLE.EIGHTEEN_TO_THIRTY_SIX:
        bundleNumbers = Array.from({ length: 18 }, (_, i) => (i + 19) as RouletteNumber)
        break
      case ROULETTE_INPUT_BUNDLE.ONE_TO_TWELVE:
        bundleNumbers = Array.from({ length: 12 }, (_, i) => (i + 1) as RouletteNumber)
        break
      case ROULETTE_INPUT_BUNDLE.THIRTEEN_TO_TWENTY_FOUR:
        bundleNumbers = Array.from({ length: 12 }, (_, i) => (i + 13) as RouletteNumber)
        break
      case ROULETTE_INPUT_BUNDLE.TWENTY_FIVE_TO_THIRTY_SIX:
        bundleNumbers = Array.from({ length: 12 }, (_, i) => (i + 25) as RouletteNumber)
        break
      case ROULETTE_INPUT_BUNDLE.FIRST_ROW:
        bundleNumbers = Array.from({ length: 12 }, (_, i) => (1 + i * 3) as RouletteNumber)
        break
      case ROULETTE_INPUT_BUNDLE.SECOND_ROW:
        bundleNumbers = Array.from({ length: 12 }, (_, i) => (2 + i * 3) as RouletteNumber)
        break
      case ROULETTE_INPUT_BUNDLE.THIRD_ROW:
        bundleNumbers = Array.from({ length: 12 }, (_, i) => (3 + i * 3) as RouletteNumber)
        break
    }

    const allSelected = bundleNumbers.every((num) => selectedNumbers.includes(num))

    if (allSelected) {
      onNumbersChange(selectedNumbers.filter((n) => !bundleNumbers.includes(n)))
    } else {
      const newNumbers = [...selectedNumbers]
      for (const num of bundleNumbers) {
        if (!newNumbers.includes(num)) {
          newNumbers.push(num)
        }
      }
      onNumbersChange(newNumbers)
    }
  }

  const renderNumberButton = (number: RouletteNumber) => {
    const color = getNumberColor(number)
    const selected = isNumberSelected(number)
    const colorStyles = getColorStyles(color)

    return (
      <Button
        key={number}
        variant="ghost"
        size="sm"
        onClick={() => handleNumberClick(number)}
        disabled={isDisabled}
        className={`relative w-[25px] h-[25px] p-0 text-[10px] leading-5 font-semibold rounded-sm shadow-none ${colorStyles} ${COMMON_BUTTON_STYLES} ${DISABLED_STYLES}`}
      >
        {selected && (
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              src={isDisabled ? chipDisabledSvg : chipSvg}
              alt="Selected"
              className="absolute w-full h-full top-0 left-0"
            />
            <span className="relative z-10 text-white text-[10px]">{number}</span>
          </div>
        )}
        {!selected && <span className="disabled:text-roulette-disabled-text">{number}</span>}
      </Button>
    )
  }

  const renderBundleButton = (
    bundle: ROULETTE_INPUT_BUNDLE,
    label?: string,
    className?: string,
  ) => {
    const isRowButton = [
      ROULETTE_INPUT_BUNDLE.FIRST_ROW,
      ROULETTE_INPUT_BUNDLE.SECOND_ROW,
      ROULETTE_INPUT_BUNDLE.THIRD_ROW,
    ].includes(bundle)

    const isColorButton = [ROULETTE_INPUT_BUNDLE.RED, ROULETTE_INPUT_BUNDLE.BLACK].includes(bundle)

    const sizeClass = isRowButton ? "w-[25px] h-[25px]" : "h-[25px]"

    let buttonStyles: string
    if (isColorButton) {
      const color: RouletteColor = bundle === ROULETTE_INPUT_BUNDLE.RED ? "red" : "black"
      buttonStyles = getColorStyles(color)
    } else {
      buttonStyles = getBundleStyles()
    }

    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleBundleClick(bundle)}
        disabled={isDisabled}
        className={`${sizeClass} px-1 text-[10px] leading-5 font-semibold rounded-sm shadow-none ${buttonStyles} ${COMMON_BUTTON_STYLES} ${DISABLED_STYLES} ${
          className || ""
        }`}
      >
        {label}
      </Button>
    )
  }

  // New layout based on the image - correct number arrangement
  const numberGrid = [
    [4, 8, 12, 16, 20, 24, 28, 32, 36],
    [3, 7, 11, 15, 19, 23, 27, 31, 35],
    [2, 6, 10, 14, 18, 22, 26, 30, 34],
    [1, 5, 9, 13, 17, 21, 25, 29, 33],
  ]

  return (
    <>
      <GameMultiplierDisplay multiplier={multiplier} className="top-[23px]" />
      <div className="absolute bottom-[8px] left-[9.5px]">
        <div className="w-[285px] h-[156px] space-y-[1px]">
          <div className="flex gap-[1px]">
            <div className="flex flex-col justify-stretch h-[103px]">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNumberClick(0)}
                disabled={isDisabled}
                className={`relative w-[25px] h-full p-0 text-[12px] leading-5 font-bold rounded-sm shadow-none ${getColorStyles(
                  "green",
                )} ${COMMON_BUTTON_STYLES} ${DISABLED_STYLES}`}
              >
                {isNumberSelected(0) && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <img
                      src={isDisabled ? chipDisabledSvg : chipSvg}
                      alt="chip"
                      className="absolute w-full h-full top-0 left-0 object-contain"
                    />
                    <span className="relative z-10 text-white font-bold text-[12px]">0</span>
                  </div>
                )}
                {!isNumberSelected(0) && (
                  <span className="disabled:text-roulette-disabled-text">0</span>
                )}
              </Button>
            </div>

            {/* Main number grid */}
            <div className="grid grid-rows-4 gap-[1px]">
              {numberGrid.map((row, rowIndex) => (
                <div key={rowIndex} className="grid grid-cols-9 gap-[1px]">
                  {row.map((number) => renderNumberButton(number as RouletteNumber))}
                </div>
              ))}
            </div>

            {/* 2:1 buttons on the right */}
            <div className="flex flex-col gap-[1px]">
              {renderBundleButton(ROULETTE_INPUT_BUNDLE.THIRD_ROW, "2:1")}
              {renderBundleButton(ROULETTE_INPUT_BUNDLE.SECOND_ROW, "2:1")}
              {renderBundleButton(ROULETTE_INPUT_BUNDLE.FIRST_ROW, "2:1")}
              {renderBundleButton(ROULETTE_INPUT_BUNDLE.FIRST_ROW, "2:1")}
            </div>
          </div>

          {/* Bottom betting options */}
          <div className="space-y-[1px]">
            {/* 1 to 12 row */}
            <div className="flex gap-[1px]">
              <div className="grid grid-cols-3 gap-[1px] flex-1">
                {renderBundleButton(ROULETTE_INPUT_BUNDLE.ONE_TO_TWELVE, "1 to 12", "flex-1")}
                {renderBundleButton(
                  ROULETTE_INPUT_BUNDLE.THIRTEEN_TO_TWENTY_FOUR,
                  "13 to 24",
                  "flex-1",
                )}
                {renderBundleButton(
                  ROULETTE_INPUT_BUNDLE.TWENTY_FIVE_TO_THIRTY_SIX,
                  "25 to 36",
                  "flex-1",
                )}
              </div>
            </div>

            {/* Even/Odd/Colors row */}
            <div className="flex gap-[1px]">
              <div className="flex gap-[1px] flex-1">
                {renderBundleButton(ROULETTE_INPUT_BUNDLE.ONE_TO_EIGHTEEN, "1 to 18", "flex-1")}
                {renderBundleButton(ROULETTE_INPUT_BUNDLE.EVEN, "Even", "flex-1")}
                {renderBundleButton(ROULETTE_INPUT_BUNDLE.BLACK, undefined, "flex-1")}
                {renderBundleButton(ROULETTE_INPUT_BUNDLE.RED, undefined, "flex-1")}
                {renderBundleButton(ROULETTE_INPUT_BUNDLE.ODD, "Odd", "flex-1")}
                {renderBundleButton(
                  ROULETTE_INPUT_BUNDLE.EIGHTEEN_TO_THIRTY_SIX,
                  "19 to 36",
                  "flex-1",
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
